<?php declare(strict_types=1);

namespace App\Tests\Model\Cart;

use App\Model\Cart\CartService;
use App\Model\Cart\CartItemDto;
use App\Tests\Helpers\Fake\Session;
use Tester\Assert;
use Tester\TestCase;

require __DIR__ . '/../../bootstrap-integration.php';

/**
 * @testCase
 */
final class CartServiceTest extends TestCase
{
    private CartService $cartService;
    private Session $session;

    protected function setUp(): void
    {
        $this->session = new Session();
        $this->cartService = new CartService($this->session);
    }

    public function testEmptyCartInitially(): void
    {
        Assert::true($this->cartService->isEmpty());
        Assert::same([], $this->cartService->getAllItems());
        Assert::same(0, $this->cartService->getTotalItemsCount());
    }

    public function testAddItem(): void
    {
        $this->cartService->addItem(1, 2);
        
        Assert::false($this->cartService->isEmpty());
        Assert::same(1, count($this->cartService->getAllItems()));
        Assert::same(2, $this->cartService->getTotalItemsCount());
        
        $item = $this->cartService->getItem(1);
        Assert::notNull($item);
        Assert::same(1, $item->id);
        Assert::same(2, $item->count);
    }

    public function testAddMultipleItems(): void
    {
        $this->cartService->addItem(1, 2);
        $this->cartService->addItem(2, 3);
        
        Assert::same(2, count($this->cartService->getAllItems()));
        Assert::same(5, $this->cartService->getTotalItemsCount());
    }

    public function testAddSameItemIncreasesQuantity(): void
    {
        $this->cartService->addItem(1, 2);
        $this->cartService->addItem(1, 3);
        
        Assert::same(1, count($this->cartService->getAllItems()));
        Assert::same(5, $this->cartService->getTotalItemsCount());
        
        $item = $this->cartService->getItem(1);
        Assert::same(5, $item->count);
    }

    public function testUpdateItemQuantity(): void
    {
        $this->cartService->addItem(1, 2);
        $this->cartService->updateItemQuantity(1, 5);
        
        $item = $this->cartService->getItem(1);
        Assert::same(5, $item->count);
        Assert::same(5, $this->cartService->getTotalItemsCount());
    }

    public function testUpdateItemQuantityToZeroRemovesItem(): void
    {
        $this->cartService->addItem(1, 2);
        $this->cartService->updateItemQuantity(1, 0);
        
        Assert::true($this->cartService->isEmpty());
        Assert::null($this->cartService->getItem(1));
    }

    public function testUpdateItemQuantityToNegativeRemovesItem(): void
    {
        $this->cartService->addItem(1, 2);
        $this->cartService->updateItemQuantity(1, -1);
        
        Assert::true($this->cartService->isEmpty());
        Assert::null($this->cartService->getItem(1));
    }

    public function testRemoveItem(): void
    {
        $this->cartService->addItem(1, 2);
        $this->cartService->addItem(2, 3);
        
        $this->cartService->removeItem(1);
        
        Assert::same(1, count($this->cartService->getAllItems()));
        Assert::null($this->cartService->getItem(1));
        Assert::notNull($this->cartService->getItem(2));
    }

    public function testClearCart(): void
    {
        $this->cartService->addItem(1, 2);
        $this->cartService->addItem(2, 3);
        
        $this->cartService->clearCart();
        
        Assert::true($this->cartService->isEmpty());
        Assert::same([], $this->cartService->getAllItems());
    }

    public function testGetNonExistentItem(): void
    {
        Assert::null($this->cartService->getItem(999));
    }

    public function testGetAllItemsReturnsCartItemDtos(): void
    {
        $this->cartService->addItem(1, 2);
        $this->cartService->addItem(2, 3);
        
        $items = $this->cartService->getAllItems();
        
        Assert::same(2, count($items));
        Assert::type(CartItemDto::class, $items[0]);
        Assert::type(CartItemDto::class, $items[1]);
    }
}

(new CartServiceTest())->run();
