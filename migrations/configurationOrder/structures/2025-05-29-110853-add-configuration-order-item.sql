CREATE TABLE `configuration_order_item`
(
	`id`                   int(11) NOT NULL AUTO_INCREMENT PRIMARY KEY,
	`selectedOptions`      text NOT NULL,
	`count`                int(11) NULL,
	`configurationOrderId` int(11) NOT NULL,
	`productInstanceId`    int(11) NULL,
	`variantId`            int(11) NOT NULL,
	FOREI<PERSON><PERSON> KEY (`configurationOrderId`) REFERENCES `configuration_order` (`id`) ON DELETE CASCADE,
	FOREIGN KEY (`productInstanceId`) REFERENCES `product_instance` (`id`) ON DELETE CASCADE,
	FOREIGN KEY (`variantId`) REFERENCES `product_variant` (`id`) ON DELETE RESTRICT
) ENGINE='InnoDB' COLLATE 'utf8mb4_unicode_520_ci';

INSERT INTO `configuration_order_item` (
	`selectedOptions`,
	`count`,
	`configurationOrderId`,
	`productInstanceId`,
	`variantId`
)
SELECT
	co.`selectedOptions`,
	co.`count`,
	co.`id` AS `configurationOrderId`, -- Renaming 'id' from configuration_order to match the new table's FK
	co.`productInstanceId`,
	co.`variantId`
FROM
	`configuration_order` AS co;
