<?php declare(strict_types = 1);

namespace App\FrontModule\Presenters\Product;

use Apitte\Core\Exception\Api\ClientErrorException;
use Apitte\Core\Http\ApiResponse;
use App\Components\MessageForForm\MessageForForm;
use App\Components\MessageForForm\MessageForFormFactory;
use App\FrontModule\Components\ConfigurationOrderForm\ConfigurationOrderForm;
use App\FrontModule\Components\ConfigurationOrderForm\ConfigurationOrderFormFactory;
use App\FrontModule\Components\CustomContentRenderer\HasCustomContentRenderer;
use App\FrontModule\Components\WatchdogForm\WatchdogForm;
use App\FrontModule\Components\WatchdogForm\WatchdogFormFactory;
use App\FrontModule\Components\ProductParameters\ProductParameters;
use App\FrontModule\Components\ProductParameters\ProductParametersFactory;
use App\FrontModule\Components\SectionsList\SectionsList;
use App\FrontModule\Components\SectionsList\SectionsListFactory;
use App\FrontModule\Components\SectionsMenu\SectionsMenu;
use App\FrontModule\Components\SectionsMenu\SectionsMenuFactory;
use App\FrontModule\Presenters\BasePresenter;
use App\Model\Configurator\SetupBuilder;
use App\Model\Gtm\GtmProductViewEvent;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Parameter\Parameter;
use App\Model\Orm\ParameterValue\ParameterValueRepository;
use App\Model\Orm\Product\Product;
use App\Model\Orm\ProductLocalization\ProductLocalization;
use App\Model\Orm\ProductVariant\ProductVariant;
use App\Model\TonDocuments\ProductDocumentsFactory;
use App\Model\TonDocuments\ProductSections;
use App\PostType\Collection\Model\Orm\CollectionLocalizationRepository;
use App\PostType\ProductInstance\Model\Orm\ProductInstanceLocalization;
use Nette\Application\Attributes\Persistent;
use Nette\Application\BadRequestException;
use Nextras\Orm\Collection\ICollection;

/**
 * @method ProductVariant getObject()
 */
class ProductPresenter extends BasePresenter
{
	use HasCustomContentRenderer;

	private ?ProductVariant $variant;

	#[Persistent]
	public ?int $productInstance = null;

	private Product $product;

	private ProductLocalization $productLocalization;
	private ?ProductInstanceLocalization $productInstanceLocalization = null;
	private array $sections = [];

	public function __construct(
		private readonly string $configuratorUrl,
		private readonly MessageForFormFactory $messageForFormFactory,
		private readonly ProductParametersFactory $productParametersFactory,
		private readonly SetupBuilder $setupBuilder,
		private readonly ProductDocumentsFactory $productDocumentsFactory,
		private readonly ConfigurationOrderFormFactory $configurationOrderFormFactory,
		private readonly CollectionLocalizationRepository $collectionLocalizationRepository,
		private readonly SectionsMenuFactory $sectionsMenuFactory,
		private readonly SectionsListFactory $sectionsListFactory,
		private readonly ProductSections $productSections,
		private readonly parameterValueRepository $parameterValueRepository,
	)
	{
		parent::__construct();
	}


	public function actionDetail(ProductLocalization $object, mixed $v = null, bool $inB2b = false): void
	{
		$this->inB2b = $inB2b;
		$variantId = $v;
		$this->productLocalization = $object;
		$this->product = $this->productLocalization->product;

		$isProductInstance = (int)$this->getParameter("productInstance") > 0;

		if (!$isProductInstance && $this->product->hideInCatalog) {
			throw new BadRequestException('Page not found');
		}

		if ($variantId !== null) {
			$this->variant = $object->getActiveVariantByMutation($this->mutation, $variantId);
			if ($this->variant !== null && $this->variant->product->id !== $this->product->id) {
				$this->redirect($this->productLocalization);
			}
		} else {
			$this->variant = $object->getFirstActiveVariantByMutation($this->mutation);
		}

		if ($this->variant === null && $this->getParameter('show') === 1) {
			$this->variant = $this->product->firstVariant;
		}

		if ($this->productInstance && ($productInstanceLocalization = $this->orm->productInstanceLocalization->getById($this->productInstance))) {
			$this->productInstanceLocalization = $productInstanceLocalization;
			$this->variant =  $productInstanceLocalization->productInstance->variant;
		}

		$this->setObject($this->productLocalization);

	}


	public function renderDetail(): void
	{
		$variantLocalizationCarousel = [];
		if (isset($this->product->cf->variants_carousel_2->items)
			&& ($variants = $this->product->cf->variants_carousel_2->items)
			&& $variants instanceof ICollection)
		{
			$variantLocalizationCarousel = $this->orm->productVariantLocalization->findByVariantsIds($variants, $this->mutation);
		}
		$this->template->variantLocalizationCarousel = $variantLocalizationCarousel;
		if ( ! $this->product->disableConfigurator) {
			$this->template->configuratorSetup = $this->setupBuilder->getSetup($this->mutation, $this->product, $this->variant, $this->inB2b);
		}

		$woodTypeTranslation = "";
		if (isset($this->productInstanceLocalization->productInstance) && !empty($this->productInstanceLocalization->productInstance->wood)){

			$woodType = $this->productInstanceLocalization->productInstance->wood;
			$woodTypeTranslation = $woodType;

			$woodTypeValue = $this->parameterValueRepository->getBy([
				'internalAlias' => $woodType,
				'parameter->uid' => Parameter::UID_WOOD
			]);

			if ($woodTypeValue !== null) {
				$woodTypeTranslation = $woodTypeValue->value;
			}
		}
		$this->template->woodTypeTranslation = $woodTypeTranslation;

		if (isset($this->productInstanceLocalization->productInstance) && $this->productInstanceLocalization->productInstance->stock > 0) {

			$timeToDelivery = 'now';

			if ($this->productInstanceLocalization->productInstance->colorOption && $this->productInstanceLocalization->productInstance->coverOption) {
				$timeToDelivery = 'cover-color-2-3';
			} else if ($this->productInstanceLocalization->productInstance->colorOption) {
				$timeToDelivery = 'color-2-3';
			} else if ($this->productInstanceLocalization->productInstance->coverOption) {
				$timeToDelivery = 'cover-1-2';
			}

			$timeToDeliveryTranslation = $this->translator->translate('filter_value_' . $timeToDelivery);
			if ($timeToDeliveryTranslation) {
				$this->template->timeToDeliveryTranslation = $timeToDeliveryTranslation;
			}
		}

		$this->template->specialServices = [];
		$this->template->product = $this->product;
		$this->template->productLocalization = $this->productLocalization;
		$this->template->variant = $this->variant;
		$this->template->productInstanceLocalization = $this->productInstanceLocalization;
		$this->template->configuratorType = $this->getConfiguratorType();
		$this->template->configuratorUrl = $this->configuratorUrl;

		if ($this->mutation->langCode === Mutation::CODE_SK) {
			$this->template->configuratorLocale = 'sk-SK';
			$this->template->configuratorCurrency = 'EUR';
		} else {
			$this->template->configuratorLocale = 'cs-CZ';
			$this->template->configuratorCurrency = 'CZK';
		}
		$this->addDocumentToTemplate();

		$this->template->collectionLocalization = $this->collectionLocalizationRepository->getByProductAndMutation($this->product, $this->mutation);
		// $this->gtm->pushEvent((new GtmProductViewEvent($this->gtm))->setup($this->mutation, $this->variant));

	}


	public function actionPreorder(int $id): void
	{
		if ($this->isAjax()) {
			$this->presenter->setLayout(false);
		}
	}

	protected function createComponentMessageForForm(): MessageForForm
	{
		return $this->messageForFormFactory->create($this->translator);
	}

	protected function createComponentProductParameters(): ProductParameters
	{
		return $this->productParametersFactory->create($this->product, $this->variant);
	}

	protected function createComponentConfigurationOrderForm(): ConfigurationOrderForm
	{
		return $this->configurationOrderFormFactory->create(
			$this->variant,
			$this->mutation,
			$this->currentState,
			configurationType: $this->getConfiguratorType(),
			user: $this->userEntity,
			productInstanceLocalization: $this->productInstanceLocalization,
		);
	}

	private function addDocumentToTemplate(): void
	{
		// bd( $this->variant->id);
		if ($this->inB2b) {
			$sections = $this->productSections->getSections($this->mutation, $this->variant, $this->userEntity);
			$this->sections = $sections;
			$this->template->sections = $sections;

		} else {
			$productDocuments = $this->productDocumentsFactory->create($this->variant);
			$downloadGroups = [];
			$normalDocuments = $productDocuments->findNormalDocuments($this->mutation);
			$premiumDocuments = $productDocuments->findPremiumDocuments();

			if ($normalDocuments !== []) {
				$downloadGroups[] = (object) ['title' => "Basic Materials", 'items' => $normalDocuments];
			}
			if ($premiumDocuments !== []) {
				$downloadGroups[] = (object) ['title' => "Premium Materials", 'items' => $premiumDocuments];
			}

			$this->template->documentGroups = $downloadGroups;
		}
	}


	protected function createComponentSectionsMenu(): SectionsMenu
	{
		return $this->sectionsMenuFactory->create($this->sections);
	}

	protected function createComponentSectionsList(): SectionsList
	{
		return $this->sectionsListFactory->create($this->sections, $this->productLocalization, $this->variant);
	}



}
