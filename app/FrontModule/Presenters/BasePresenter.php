<?php declare(strict_types = 1);

namespace App\FrontModule\Presenters;

use App\Exceptions\LogicException;
use App\FrontModule\Components\AntispamSession;
use App\FrontModule\Components\Breadcrumb\Breadcrumb;
use App\FrontModule\Components\Breadcrumb\BreadcrumbFactory;
use App\FrontModule\Components\CanonicalUrl\CanonicalUrl;
use App\FrontModule\Components\CanonicalUrl\CanonicalUrlFactory;
use App\FrontModule\Components\ContactForm\ContactForm;
use App\FrontModule\Components\ContactForm\ContactFormFactory;
use App\FrontModule\Components\ContactForm\ContactFormStatus;
use App\FrontModule\Components\EditButton\EditButton;
use App\FrontModule\Components\EditButton\EditButtonFactory;
use App\FrontModule\Components\Menu\Menu;
use App\FrontModule\Components\Menu\MenuFactory;
use App\FrontModule\Components\NewsletterForm\NewsletterForm;
use App\FrontModule\Components\NewsletterForm\NewsletterFormFactory;
use App\FrontModule\Components\Robots\Robots;
use App\FrontModule\Components\Robots\RobotsFactory;
use App\FrontModule\Components\SignInForm\SignInForm;
use App\FrontModule\Components\SignInForm\SignInFormFactory;
use App\FrontModule\Components\Suggest\Suggest;
use App\FrontModule\Components\Suggest\SuggestFactory;
use App\FrontModule\Components\ToggleLanguage\ToggleLanguage;
use App\FrontModule\Components\ToggleLanguage\ToggleLanguageFactory;
use App\FrontModule\Components\ToggleLanguage\ToggleLanguageModel;
use App\FrontModule\Presenters\Homepage\HomepagePresenter;
use App\Infrastructure\BasicAuth\Authenticator;
use App\Infrastructure\Latte\Filters;
use App\Model\CustomField\LazyValue;
use App\Model\Gtm\Gtm;
use App\Model\Gtm\GtmLocalizationEvent;
use App\Model\Gtm\GtmLoggedUserEvent;
use App\Model\ImageResizerWrapper;
use App\Model\IpToStateDetector;
use App\Model\Link\LinkFactory;
use App\Model\Mutation\BrowserMutationDetector;
use App\Model\Mutation\MutationDetector;
use App\Model\Mutation\MutationHolder;
use App\Model\Orm\ConfigurationOrder\ConfigurationOrder;
use App\Model\Orm\ConfigurationOrder\ConfigurationType;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\PriceLevel\PriceLevel;
use App\Model\Orm\Product\Product;
use App\Model\Orm\ProductVariant\ProductVariant;
use App\Model\Orm\Routable;
use App\Model\Orm\State\State;
use App\Model\Orm\User\User;
use App\Model\PagesFactory;
use App\Model\Security\UserStorageSwitcher;
use App\Model\TonDocuments\LinkRenderer;
use App\PostType\Page\Model\Orm\CatalogTree;
use App\PostType\Page\Model\Orm\CommonTree;
use App\PostType\Page\Model\Orm\Tree;
use App\PostType\Page\Model\Orm\TreeModel;
use App\Model\StaticPage\StaticPage;
use App\Model\TranslatorDB;
use App\PostType\Contact\Model\Orm\ContactLocalizationFinder;
use App\PostType\Core\Model\LocalizationEntity;
use App\PostType\SeoLink\Model\Orm\SeoLinkLocalization;
use Jaybizzle\CrawlerDetect\CrawlerDetect;
use Nette;
use Nette\Application\Attributes\Persistent;
use Nette\Application\Request;
use Nette\Caching\Cache;
use Nette\DI\Attributes\Inject;
use Tracy\Debugger;
use function assert;

abstract class BasePresenter extends \App\BasePresenter
{
	#[Persistent]
	public string $alias;
	#[Inject]
	public Nette\Http\RequestFactory $requestFactory;

	#[Inject]
	public ToggleLanguageFactory $toggleLanguageFactory;

	#[Inject]
	public Nette\Http\Session $session;

	#[Inject]
	public Nette\Caching\Storage $cacheStorage;

	#[Inject]
	public TranslatorDB $translator;

	#[Inject]
	public ContactLocalizationFinder $contactLocalizationFinder;

	#[Inject]
	public PagesFactory $pagesFactory;

	#[Inject]
	public MutationHolder $mutationHolder;

	#[Inject]
	public MutationDetector $mutationDetector;

	#[Inject]
	public LinkFactory $linkFactory;

	#[Inject]
	public ImageResizerWrapper $imageResizerWrapper;

	#[Inject]
	public ContactFormFactory $contactFormFactory;

	#[Inject]
	public NewsletterFormFactory $newsletterFormFactory;

	#[Inject]
	public SignInFormFactory $signInFormFactory;

	#[Inject]
	public BreadcrumbFactory $breadcrumbFactory;

	#[Inject]
	public MenuFactory $menuFactory;

	#[Inject]
	public RobotsFactory $robotsFactory;
	#[Inject]
	public CanonicalUrlFactory $canonicalUrlFactory;
	#[Inject]
	public TreeModel $treeModel;
	#[Inject]
	public EditButtonFactory $editButtonFactory;
	#[Inject]
	public IpToStateDetector $ipToStateDetector;
	#[Inject]
	public UserStorageSwitcher $userStorageSwitcher;

	#[Inject]
	public Gtm $gtm;

	#[Inject]
	public CrawlerDetect $crawlerDetect;
	#[Inject]
	public BrowserMutationDetector $browserMutationDetector;
	protected Routable|StaticPage $object;
	protected Mutation $mutation;
	protected Cache $cache;

	#[Inject]
	public SuggestFactory $suggestFactory;

	#[Inject]
	public ToggleLanguageModel $toggleLanguageModel;

	protected ?User $userEntity = null;

	/**
	 * state = kolize s persistant v NewsletterPresenter
	 */
	protected State $currentState;

	protected PriceLevel $priceLevel;

	#[Inject]
	public Authenticator $basicAuth;

	#[Inject]
	public AntispamSession $antispamSession;

	#[Persistent]
	public bool $inB2b = false;

	protected bool $bypassBasicAuth = false;

	protected bool $enableAdministrationLinks = false;

	#[Inject]
	public LinkRenderer $tonLinkRenderer;
	protected ?SeoLinkLocalization $seoLink = null;


	public function tryDetectAndRedirectToMutation(): void
	{
		if ((int)$this->getParameter('show') === 1) {
			return;
		}

		$hash = $this->getParameter('hash');
		if ($hash !== null && $hash !== '') {
			return;
		}

		if (!$this->crawlerDetect->isCrawler() && $this->getObject() instanceof Routable) {
			$this->browserMutationDetector->detectAndTryRedirect($this->getObject(),
				function (
					LocalizationEntity $localization,
					Mutation $detectMutation,
				): void {
					if ($localization->getMutation() !== $detectMutation) {
						$newLocalization = $localization->getParent()->getLocalization($detectMutation);
						if ($newLocalization instanceof Routable) {
							$this->redirect($newLocalization, ['mutation' => $newLocalization->getMutation()]);
						}
					}
				}
			);
		}
	}

	protected function cleanRequestParametersBeforeStore(): void
	{
		$parameters = $this->getRequest()->getParameters();
		$parameters = array_map(fn($parameter) => is_object($parameter) ? null : $parameter, $parameters);
		$this->getRequest()->setParameters($parameters);
	}

	protected function startup(): void
	{
		// ******* redirects ************************
		if (isset($_GET['terminate'])) {
			$this->terminate();
		}

		if ($this->request->getParameter('show') !== null) {
			$this->orm->setPublicOnly(false);
		}

		if ($this->request->hasFlag(Request::RESTORED)) { // reseni pro backlink po ajaxu
			$this->redirect('this');
		}

		// ******* basic ************************
		parent::startup();

		$this->dbaLog->register();

		// redirect old pages for /cz/* missing in redirect table
		$this->detectSecondaryRedirect();

		$this->setMutation();
		$this->setState();
		$this->setPriceLevel();
		$this->cache = new Cache($this->cacheStorage);
		$this->gtm->setMutation($this->mutation);

		if (isset($this->user->id) && $this->user->isLoggedIn()) {
			$userEntity = $this->orm->user->getById($this->user->id);
			if ($userEntity && $userEntity->id) {
				$this->userEntity = $userEntity;
			} else {
				$this->user->logout(true);
			}
		} else {
			if ($this->user->id) {
				$this->user->logout(true);
			}
		}


//		$this->autoCanonicalize = FALSE;

		// ******* helpers & templates ************************
		Filters::$mutation = $this->mutationHolder->getMutation();
		Filters::$translator = $this->translator;
		Filters::$version = $this->configService->get('webVersion');

		// prepsani defaultnich hlasek
		Nette\Forms\Validator::$messages[Nette\Forms\Form::EMAIL] = 'form_valid_email';
		Nette\Forms\Validator::$messages[Nette\Forms\Form::FILLED] = 'form_error';

//		bd(setlocale(LC_ALL, $this->mutation->locale));
		// ******* other ************************
	}

	public function detectSecondaryRedirect(): void
	{
		if ($this->request->presenterName === 'Front:Error' && Nette\Utils\Strings::startsWith($this->getHttpRequest()->getUrl()->path, '/cz/')) {
			$newAlias = preg_replace('/^\/cz\//', '', $this->getHttpRequest()->getUrl()->path);
			$url = $this->getHttpRequest()->getUrl();
			$newUrl = $url->withPath($newAlias);
			$newUrl->getAbsoluteUrl();
			$this->redirectUrl($newUrl->getAbsoluteUrl());
		}
	}

	public function checkRequirements(mixed $element): void
	{
		// check superadmin logged user
		$this->userStorageSwitcher->switch($this->getUser(), UserStorageSwitcher::SECTION_BACKEND);
		$this->enableAdministrationLinks = $this->getUser()->isLoggedIn();

		// return web user storage
		$this->userStorageSwitcher->switch($this->getUser(), UserStorageSwitcher::SECTION_FRONTEND);

		parent::checkRequirements($element);

		if ( ! $element instanceof Nette\Application\UI\ComponentReflection) {
			return;
		}

		if ($this->bypassBasicAuth) {
			return;
		}

		$this->basicAuth->authenticate(
			$this->getHttpRequest(),
			function (): never {
				$this->getHttpResponse()->setHeader('WWW-Authenticate', 'Basic realm="app"');
				if (Debugger::isEnabled()) {
					$this->terminate();
				} else {
					$this->error(httpCode: Nette\Http\IResponse::S401_Unauthorized);
				}
			},
		);
	}

	protected function setMutation(): void
	{
		if ($this->getParameter('mutation')) {
			$this->mutation = $this->getParameter('mutation');
		} else {
			$this->mutation = $this->mutationDetector->detect();
		}

		$this->mutationHolder->setMutation($this->mutation);
		$this->orm->setMutation($this->mutation);
	}


	/**
	 * Urceni statu podle cookie (user si nekde zvolil stat)
	 * nebo defaultne prvni stat mutace
	 *
	 * @throws LogicException
	 */
	protected function setState(): void
	{
		$currentState = null;
		$idState = (int) $this->getHttpRequest()->getCookie(State::COOKIE_NAME_SELECTED_STATE);

//		if ($idState) {
//			$currentState = $this->orm->state->getById($idState);
//		} else {
			$currentState = $this->ipToStateDetector->detect($this->mutation);
//		}

		// bd($currentState);

		if (!$currentState && isset($this->mutation->cf->mutationData->defaultState)) {
			$currentState = $this->mutation->cf->mutationData->defaultState->getEntity();
		}

		if (!$currentState) {
			$currentState = $this->mutation->getDefaultState();
		}

		if (!$currentState) {
			$currentState = $this->orm->state->getBy(['code' => 'CZ']);
		}

		if (!$currentState) {
			throw new LogicException('Unknown current state');
		}

		$this->currentState = $currentState;
	}

	protected function setPriceLevel(): void
	{
		$priceLevelId = $this->userEntity?->priceLevel->id ?? PriceLevel::DEFAULT_ID;
		$this->priceLevel = $this->orm->priceLevel->getById($priceLevelId);
	}

	protected function beforeRender(): void
	{
		parent::beforeRender();
		$this->tryDetectAndRedirectToMutation();

		if ($this->inB2b && $this->userEntity === null) {

			if ($this->object === $this->mutation->pages->userLogin
				|| $this->object === $this->mutation->pages->registration
				|| $this->object === $this->mutation->pages->lostPassword
				|| $this->object === $this->mutation->pages->resetPassword
				|| $this->object === $this->mutation->pages->search
			) {
				// can show this page

			} else {
				$this->cleanRequestParametersBeforeStore();
				$this->redirect($this->mutation->pages->userLogin, ['inB2b' => true, 'backlink' => $this->storeRequest()]);
			}
		}

		if ($this->userEntity !== null && ! $this->isAjax()) {
			$this->gtm->pushEvent(
				(new GtmLoggedUserEvent($this->gtm))->setup($this->userEntity)
			);
		}

		$this->antispamSession->prepareAntispam();

		// ******* basic ************************
		$this->template->setTranslator($this->translator);
		if (!$this->isAjax()) {
			$this->gtm->pushEvent((new GtmLocalizationEvent($this->gtm))->setup($this->mutation));
		}
		$this->template->gtm = $this->gtm;
		$this->template->mutation = $this->mutationHolder->getMutation();
		$this->template->imageObjectFactory = $this->imageObjectFactory;
		$this->layout = FE_TEMPLATE_DIR . '/@layout.latte';
		$this->template->templates = FE_TEMPLATE_DIR;
		$this->template->tonLinkRenderer = $this->tonLinkRenderer;

		$this->template->isHomepage = $this instanceof HomepagePresenter;
		$this->template->isB2BHomepage = $this->object === $this->mutation->pages->userSection;

		$this->template->hrefLangs = $this->getHrefLangs();

		if (!isset($this->inB2b)) {
			$this->inB2b = ($this->object instanceof Tree && $this->treeModel->isB2b($this->object));
		}

		if ($this->inB2b) {
			$linkToHome = $this->mutation->pages->userSection;
			$objectFooterCf = (isset($this->mutation->cf->footer_menu_bz)) ? $this->mutation->cf->footer_menu_bz : null;
		} else {
			$linkToHome = $this->mutation->pages->title;
			$objectFooterCf = (isset($this->mutation->cf->footer_menu)) ? $this->mutation->cf->footer_menu : null;
		}

		$this->template->objectFooterCf = $objectFooterCf;
		$this->template->linkToHome = $linkToHome;
		$this->template->inB2b = $this->inB2b;

		//  robots per mutation
		if ($this->mutation->langCode) {
			$this->template->currencyCode = $this->mutation->currency; //"CZK";
		}

		$this->template->object = $this->getObject();
		$this->template->userEntity = $this->userEntity;
		$this->template->pages = $this->mutationHolder->getMutation()->pages;
		$this->template->state = $this->currentState;
		$this->template->priceLevel = $this->priceLevel;
		$this->template->translator = $this->translator;

		// ******* callbacks ********************
		$this->template->getImage = function ($entity, $size) {
			return $this->imageResizerWrapper->getResizedImage($entity, $size);
		};

		$this->template->cfg = function () {
			return call_user_func_array([$this->configService, 'get'], func_get_args());
		};

		// ******* other ********************
		if (isset($this->object->template) && $this->object->template) {
			$_presenterName = explode(':', $this->object->template);
			if (isset($_presenterName[0])) {
				if (isset($this->object->parent) && $this->object->parent === null) {
					$this->template->presenterName = 'Homepage';
				} else {
					$this->template->presenterName = $_presenterName[0];
				}
			}
		}

		$this->template->googleAnalyticsCode = $this->mutationHolder->getMutation()->getGACode();
		$this->template->googleApiKey = $this->configService->getParam('google', 'apiKey');
//		$this->setTemplateMeasureIp();
	}

	protected function handleMeasureIp(): void
	{
		$this->template->measureIp = base64_encode($this->configService->get('REMOTE_ADDR'));

		if ($this->userEntity && isset($this->userEntity->id)) {
			$this->template->measureUserId = $this->userEntity->id;
			if ($this->userEntity->createdTime) {
				$this->template->measureUserCreated = $this->userEntity->createdTime->format('Y-d-m');
			} else {
				$this->template->measureUserCreated = null;
			}
		} else {
			$this->template->measureUserId = null;
			$this->template->measureUserCreated = null;
		}
	}


	public function setObject(Routable|StaticPage $object): void
	{
		$this->object = $object;
	}


	public function getObject(): Routable|StaticPage
	{
		return $this->object;
	}


	public function handleLogout(): void
	{
		$this->getUser()->logout(true);
		$this->flashMessage('msg_info_logout');
		$this->inB2b = false;
		$this->redirect($this->mutation->pages->title);
	}


	// ************************** COMPONENTS ****************************** /

	protected function createComponentBreadcrumb(): Breadcrumb
	{
		return $this->breadcrumbFactory->create($this->getObject(), $this->inB2b);
	}

	protected function createComponentSignInForm(): SignInForm
	{
		return $this->signInFormFactory->create($this->mutation, $this->object);
	}

	protected function createComponentSignInFormHeader(): SignInForm
	{
		return $this->signInFormFactory->create($this->mutation, $this->object);
	}

	protected function createComponentMenu(): Menu
	{
		return $this->menuFactory->create($this->getObject(), $this->inB2b, $this->userEntity);
	}



	protected function createComponentNewsletterForm(): NewsletterForm
	{
		return $this->newsletterFormFactory->create($this->getObject());
	}

	protected function createComponentCanonicalUrl(): CanonicalUrl
	{
		$routable = $this->seoLink ?? $this->getObject();
		return $this->canonicalUrlFactory->create($routable, $this->linkFactory, $this['robots']);
	}


	protected function createComponentRobots(): Robots
	{
		$routable = $this->seoLink ?? $this->getObject();
		return $this->robotsFactory->create($routable, $this->mutationHolder->getMutation());
	}


	protected function createComponentEditButton(): EditButton
	{
		$routable = $this->getObject();
		if ($routable instanceof Routable) {
			return $this->editButtonFactory->create($routable, $this->enableAdministrationLinks, $this->params);
		} else {
			return $this->editButtonFactory->create(null, $this->enableAdministrationLinks);
		}

	}

	public function createComponentToggleLanguage(): ToggleLanguage
	{
		$page = $this->getObject();
		if ($page instanceof LocalizationEntity) {
			return $this->toggleLanguageFactory->create($page, $this->enableAdministrationLinks);
		} else {
			assert($page instanceof StaticPage);
			$page = $this->pagesFactory->create($this->mutation)->title;
			return $this->toggleLanguageFactory->create($page, $this->enableAdministrationLinks);
		}
	}

	// ****************************** INTERNALS ****************************** /

	/**
	 * @param array $args
	 * @throws Nette\Application\UI\InvalidLinkException
	 */
	public function link(Product|LazyValue|Routable|string $destination, $args = []): string
	{
		[$destination, $args] = $this->translateDestination($destination, $args);

		if ($destination instanceof Routable) {
			[$destination, $args] = $this->linkFactory->linkInPresenter($destination, $args);
		}

		return parent::link($destination, $args);
	}


	/**
	 * @throws Nette\Application\AbortException
	 */
	public function redirect(Product|LazyValue|Routable|string $destination, $args = []): never
	{
		[$destination, $args] = $this->translateDestination($destination, $args);

		if ($destination instanceof Routable) {
			[$destination, $args] = $this->linkFactory->linkInPresenter($destination, $args);
		}

		parent::redirect($destination, $args);
	}


	private function translateDestination(Product|LazyValue|Routable|string $destination, array $args): array
	{
		if ($destination instanceof LazyValue) {
			$destination = $destination->getEntity();
			if ($destination === null) {
				trigger_error('Bad CF LazyValue entity', E_USER_NOTICE);
				// value for common user without debug mode
				$destination = 'this';
			}
		}

		if (is_string($destination)) { // input: this, //this, logout!
			$mutation = $args['mutation'] ?? $this->mutation;
			if ($mutation->urlPrefix) {
				$args['urlPrefix'] = $mutation->urlPrefix;
			}
		}

		if ($destination instanceof Product) {
			$mutation = $args['mutation'] ?? $this->mutation;
			if ($mutation->urlPrefix) {
				$args['urlPrefix'] = $mutation->urlPrefix;
			}
			$destination = $destination->getLocalization($mutation);
		}

		return [$destination, $args];
	}



	public function formatTemplateFiles(): array
	{
		$fileName = static::getReflection()->getFileName();
		assert($fileName !== false);

		$dir = dirname($fileName);
		$dir = is_dir("$dir/templates") ? $dir : dirname($dir);
		return ["$dir/templates/$this->view.latte"];
	}


	protected function createComponentContactForm(): ContactForm
	{
		assert($this->object instanceof Routable);

		$contactFormStatus = new ContactFormStatus(
			stateRepository: $this->orm->state,
			contactLocalizationRepository: $this->orm->contactLocalization,
			selectedState: $this->currentState,
		);

		return $this->contactFormFactory->create($this->object, $contactFormStatus);
	}

	public function createComponentPositionForm(): ContactForm
	{
		$object = $this->getObject();
		assert($object instanceof Routable);

		$contactFormStatus = new ContactFormStatus(
			stateRepository: $this->orm->state,
			contactLocalizationRepository: $this->orm->contactLocalization,
			selectedState: $this->currentState,
			type: 'position',
			forcedContactPerson: $this->contactLocalizationFinder->getContactLocalization($object),
		);

		return $this->contactFormFactory->create($object, $contactFormStatus);
	}

	public function createComponentCareerForm(): ContactForm
	{
		$object = $this->getObject();
		assert($object instanceof Routable);

		$contactFormStatus = new ContactFormStatus(
			stateRepository: $this->orm->state,
			contactLocalizationRepository: $this->orm->contactLocalization,
			selectedState: $this->currentState,
			type: 'career',
			forcedContactPerson: $this->contactLocalizationFinder->getContactLocalization($object),
		);

		return $this->contactFormFactory->create($object, $contactFormStatus);
	}
	public function createComponentRepairForm(): ContactForm
	{
		$object = $this->getObject();
		assert($object instanceof Routable);

		$contactFormStatus = new ContactFormStatus(
			stateRepository: $this->orm->state,
			contactLocalizationRepository: $this->orm->contactLocalization,
			selectedState: $this->currentState,
			type: 'repair',
			forcedContactPerson: $this->contactLocalizationFinder->getContactLocalization($object),
		);

		return $this->contactFormFactory->create($object, $contactFormStatus);
	}

	public function createComponentSuggest(): Suggest
	{
		return $this->suggestFactory->create($this->mutation, $this->inB2b);
	}
	public function createComponentB2bSuggest(): Suggest
	{
		return $this->suggestFactory->create($this->mutation, $this->inB2b, true);
	}


	public function setCurrentState(State $currentState): void
	{
		$this->currentState = $currentState;
	}


	public function canSee(Nette\Application\UI\Presenter $presenter): bool
	{
		return (bool)$presenter;
	}

	protected function getConfiguratorType(): ConfigurationType
	{
		return ($this->inB2b) ? ConfigurationType::Dealer : ConfigurationType::Show;
	}

	private function getHrefLangs(): array
	{

		$mutations = $this->orm->mutation->findBy(['public' => 1, 'id!='=>$this->mutation->id]);

		$hrefLangs = [];

		foreach ($mutations as $mutation) {
			//only mutations activated for production
			if (!$mutation->isInProduction()) {
				continue;
			}

			if ($this->object instanceof CatalogTree || ($this->object instanceof CommonTree && !empty($this->object->uid))) {
				$object = $this->toggleLanguageModel->getSibling($this->object, $mutation);
			} elseif ($this->object instanceof ProductVariant) {
				$variantLocalization = $this->object->getLocalization($mutation);
				$object = isset($variantLocalization) && $variantLocalization->active ? $variantLocalization->variant : null;
			} elseif ($this->object instanceof Product) {
				$productLocalization = $this->object->getLocalization($mutation);
				$object = isset($productLocalization) && $productLocalization->public ? $productLocalization->product : null;
			}

			if (isset($object) && $object instanceof Routable) {
				try {
					$args = ['mutation' => $mutation];
					$hrefLangs[$mutation->region] = $this->linkFactory->linkTranslateToNette($object, $args);
				} catch (\Throwable $e) {
					$hrefLangs[$mutation->region] = $mutation->getUrl();
				}
			} else {
				$hrefLangs[$mutation->region] = $mutation->getUrl();
			}
		}

		return $hrefLangs;
	}
}
