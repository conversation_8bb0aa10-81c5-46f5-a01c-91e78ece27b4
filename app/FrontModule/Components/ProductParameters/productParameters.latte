{default $class = $param['class'] ?? 'u-pt-xxs u-pt-sm@lg'}
{default $showCategories = isset($param['showCategories'])}

<div n:if="count($variantParameters) > 0" n:class="b-parameters, $class, $showCategories ? b-parameters--categories">
	{if $showCategories}
		<h2>
			{_"title_parameters"}
		</h2>
		<div class="b-parameters__group b-parameters__group--cols"> {* b-parameters__group--cols pokud je vic jak 4 parametrů *}
			<h3 class="b-parameters__title h2 u-mb-xs">
				{_"Size and weight"}
			</h3>
			<div class="b-parameters__list-wrap">
				<dl class="b-parameters__list">
					{foreach [
						App\Model\Orm\Parameter\Parameter::UID_WIDTH,
						App\Model\Orm\Parameter\Parameter::UID_DEPTH,
						App\Model\Orm\Parameter\Parameter::UID_HEIGHT,
						App\Model\Orm\Parameter\Parameter::UID_VYSKA_SED,

						App\Model\Orm\Parameter\Parameter::UID_SIRKA_SED,
						App\Model\Orm\Parameter\Parameter::UID_HLOUB_SED,
						App\Model\Orm\Parameter\Parameter::UID_WEIGHT,
					] as $parameterUid}
						<div class="b-parameters__param" n:if="$parameterValuesStructure = ($variantParameters[$parameterUid] ?? null)">
							<dt class="b-parameters__name">{$parameterValuesStructure->parameter->title} {if $parameterValuesStructure->parameter->unit}({$parameterValuesStructure->parameter->unit}){/if}</dt>
							<dd class="b-parameters__value">{foreach $parameterValuesStructure->values as $parameterValue}{$parameterValue->value}{sep}, {/sep}{/foreach}</dd>
						</div>

					{/foreach}
				</dl>
			</div>
		</div>
		<div class="b-parameters__group">
			<h3 class="b-parameters__title h2">
				{_"Material consumption"}
			</h3>
			<div class="b-parameters__list-wrap">
				<dl class="b-parameters__list">
					{foreach [
						App\Model\Orm\Parameter\Parameter::UID_CONSUMPTION_OF_FABRIC,
						App\Model\Orm\Parameter\Parameter::UID_LEATHER_CONSUMPTION,
						App\Model\Orm\Parameter\Parameter::UID_OBJEM_KAR1,

					] as $parameterUid}
						<div class="b-parameters__param" n:if="$parameterValuesStructure = ($variantParameters[$parameterUid] ?? null)">
							<dt class="b-parameters__name">{$parameterValuesStructure->parameter->title} {if $parameterValuesStructure->parameter->unit}({$parameterValuesStructure->parameter->unit}){/if}</dt>
							<dd class="b-parameters__value">{foreach $parameterValuesStructure->values as $parameterValue}{$parameterValue->value}{sep}, {/sep}{/foreach}</dd>
						</div>

					{/foreach}
				</dl>
			</div>
		</div>
		<div class="b-parameters__group">
			<h3 class="b-parameters__title h2">
				{_"Packaging"}
			</h3>
			<div class="b-parameters__list-wrap">
				<dl class="b-parameters__list">
					{foreach [
						App\Model\Orm\Parameter\Parameter::UID_ROZM_KAR1,
						App\Model\Orm\Parameter\Parameter::UID_STACK_COUNT,
						App\Model\Orm\Parameter\Parameter::UID_LINE_CONNECTION,
					] as $parameterUid}
						<div class="b-parameters__param" n:if="$parameterValuesStructure = ($variantParameters[$parameterUid] ?? null)">
							<dt class="b-parameters__name">{$parameterValuesStructure->parameter->title} {if $parameterValuesStructure->parameter->unit}({$parameterValuesStructure->parameter->unit}){/if}</dt>
							<dd class="b-parameters__value">{foreach $parameterValuesStructure->values as $parameterValue}
							{$parameterValue->value|replace:' ','&nbsp;'|replace:'x', '&times;'|noescape}{sep}, {/sep}{/foreach}</dd>
						</div>

					{/foreach}
				</dl>
			</div>
		</div>
	{else}
		<dl class="b-parameters__list">
			{foreach $variantParameters as $variantParameter}
				{var $parameter = $variantParameter->parameter}
				<dt class="b-parameters__name"> {$parameter->title} {if $parameter->unit}({$parameter->unit}){/if}</dt>

				{if $parameter->type == "wysiwyg"}
					<dd class="b-parameters__value">
						{foreach $variantParameter->values as $parameterValue}{$parameterValue->value|noescape}{sep}, {/sep}{/foreach}
					</dd>
				{elseif $parameter->type == "bool"}
					{if isset($variantParameter->values[0]) && $variantParameter->values[0]->internalValue == 1}
						<dd class="b-parameters__value">
							{_yes|firstUpper}
						</dd>
					{else}
						<dd class="b-parameters__value">
							{_no|firstUpper}
						</dd>
					{/if}
				{else}
					<dd class="b-parameters__value">
						{if $parameter->type == "multiselect"}
							{foreach $variantParameter->values as $parameterValue}{$parameterValue->value}{sep}, {/sep}{/foreach}
{*							{foreach $parameterValue as $valueObject}*}
{*								{if in_array($parameter->uid, $filterableUids)}*}
{*									{var $filter = ['dials' => [$parameter->uid => [$valueObject->id => $valueObject->id]]]}*}
{*									{capture $link}{plink $mainCategory, 'filter' => $filter}{/capture}*}
{*									{php $link = urldecode(htmlspecialchars_decode($link))}*}
{*									<a href="{$link}">*}
{*										{$valueObject->value}{if !$iterator->last}, {/if}*}
{*									</a>*}
{*								{else}*}
{*									{$valueObject->value}{if !$iterator->last}, {/if}*}
{*								{/if}*}
{*							{/foreach}*}
						{else}
{*							{if in_array($parameter->uid, $filterableUids)}*}
{*								{var $filter = ['dials' => [$parameter->uid => [$parameterValue->id => $parameterValue->id]]]}*}
{*								{capture $link}{plink $mainCategory, 'filter' => $filter}{/capture}*}
{*								{php $link = urldecode(htmlspecialchars_decode($link))}*}

{*								<a href="{$link}">*}
{*									{$parameterValue->value} {if $parameter->type == 'number'}{/if}*}
{*								</a>*}
{*							{else}*}
{*								{$parameterValue->value} {if $parameter->type == 'number'}{/if}*}
{*							{/if}*}

{*							{if $parameter->uid == 'rozm_kar1' || $parameter->uid == 'rozm_kar2' || $parameter->uid == 'rozm_kar3'}*}
{*								rozm_kar...*}
{*							{/if}*}

{*							{if $parameter->uid == 'weight'}*}
{*								weight...*}
{*							{/if}*}

{*							{if $parameter->uid == 'objem_kar1' || $parameter->uid == 'objem_kar2' || $parameter->uid == 'objem_kar3'}*}
{*								objem_kar...*}
{*							{/if}*}

							{foreach $variantParameter->values as $parameterValue}{$parameterValue->value|replace:' ','&nbsp;'|replace:'x', '&times;'|noescape}{sep}, {/sep}{/foreach}
						{/if}
					</dd>
				{/if}
			{/foreach}
		</dl>
	{/if}
</div>
