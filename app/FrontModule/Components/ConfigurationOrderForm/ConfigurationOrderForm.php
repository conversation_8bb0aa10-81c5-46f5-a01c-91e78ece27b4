<?php declare(strict_types=1);

namespace App\FrontModule\Components\ConfigurationOrderForm;

use App\Components\MessageForForm\MessageForForm;
use App\Components\MessageForForm\MessageForFormFactory;
use App\FrontModule\Components\FormHelpers\StateRegionHelper;
use App\FrontModule\Components\HasAntispamInput;

use App\FrontModule\Components\HasError500Catcher;
use App\Model\Form\CommonFormFactory;
use App\Model\Gtm\Gtm;
use App\Model\Gtm\GtmProductViewEvent;
use App\Model\Link\LinkFactory;
use App\Model\Orm\ConfigurationOrder\ConfigurationOrderModel;
use App\Model\Orm\ConfigurationOrder\ConfigurationOrderRepository;
use App\Model\Orm\ConfigurationOrder\ConfigurationType;
use App\Model\Orm\ConfigurationOrder\CreateConfigurationOrderDto;
use App\Model\Orm\ConfigurationOrderItem\ConfigurationOrderItemModel;
use App\Model\Orm\ConfigurationOrderItem\ConfigurationOrderItemRepository;
use App\Model\Orm\ConfigurationOrderItem\CreateConfigurationOrderItemDto;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\ProductVariant\ProductVariant;
use App\Model\Orm\State\State;
use App\Model\Orm\State\StateRepository;
use App\Model\Orm\User\User;
use App\Model\TranslatorDB;
use App\PostType\Contact\Model\Orm\ContactLocalizationRepository;
use App\PostType\ProductInstance\Model\Orm\ProductInstanceLocalization;
use App\Utils\HubspotFormService\HubspotFormService;
use Nette\Application\UI;
use Nette\Utils\ArrayHash;
use Nextras\Orm\Collection\EmptyCollection;
use Nextras\Orm\Collection\ICollection;
use Tracy\Debugger;


final class ConfigurationOrderForm extends UI\Control
{

	use HasAntispamInput;
	use HasError500Catcher;

	private ?State $forcedState = null;
	private int $price = 0;
	private array $stateIdToContactIds;

	private ICollection $states;

	private ?ICollection $contactLocalizations = null;


	public function __construct(
		private readonly ProductVariant $variant,
		private readonly Mutation $mutation,
		private State $selectedState,
		private readonly StateRepository $stateRepository,
		private readonly ContactLocalizationRepository $contactLocalizationRepository,
		private readonly ConfigurationType $configurationType,
		private readonly MessageForFormFactory $messageForFormFactory,
		private readonly CommonFormFactory $commonFormFactory,
		private readonly TranslatorDB $translator,
		private readonly ConfigurationOrderModel $configurationOrderModel,
		private readonly ConfigurationOrderItemModel $configurationOrderItemModel,
		private readonly ConfigurationOrderRepository $configurationOrderRepository,
		private readonly ConfigurationOrderItemRepository $configurationOrderItemRepository,
		private readonly StateContactModel $stateContactModel,
		private readonly StateRegionHelper $stateRegionHelper,
		private readonly Gtm $gtm,
		private readonly LinkFactory $linkFactory,
		private readonly HubspotFormService $hubspotFormService,
		private ?string $selectedOptions = '',
		private readonly ?User $user = null,
		private readonly ?ProductInstanceLocalization $productInstanceLocalization = null

	)
	{
		$this->onAnchor[] = $this->init(...);
	}



	private function init(): void
	{
		if ($this->presenter->getRequest()->getPost('state') !== null && $this->presenter->getRequest()->getPost('state') !== "") {
			$stateId = (int) $this->presenter->getRequest()->getPost('state');
			$this->setState($this->stateRepository->getByIdChecked($stateId));
		}
	}


	private function getStateIdToContactIds(): array
	{
		if (!isset($this->stateIdToContactIds)) {
			$this->stateIdToContactIds = $this->stateContactModel->getCountryMapForMutation($this->mutation);
		}

		return $this->stateIdToContactIds;
	}

	public function isCzechRepublic(): bool
	{
		if ($this->forcedState !== null) {
			if ($this->forcedState->code === State::DEFAULT_CODE) {
				return true;
			}
		} else if ($this->selectedState->code === State::DEFAULT_CODE) {
			return true;
		}
		return false;
	}

	protected function createComponentForm(): UI\Form
	{
		$form = $this->commonFormFactory->create();
		$form->setTranslator($this->translator);


		$this->stateRegionHelper->addState($form, $this->mutation, $this->selectedState, $this->getStates());
		$this->stateRegionHelper->addContact($form, $this->selectedState, $this->getContactLocalizations());

		$form->addInteger('count', 'form_label_pieces')
			->addRule($form::Min, 'minimal_number_1', 1)
			->setRequired();
		$form->addText('name', 'form_label_name_and_surname')->setRequired()->setMaxLength(255);
		$form->addEmail('email', 'form_label_email')->setRequired()->setMaxLength(255);
		$form->addText('phone', 'form_label_telephone')->setRequired()->setMaxLength(255);
		$form->addTextArea('message', 'form_label_your_message')->setRequired();
		$form->addSubmit('send');

		if ($this->isCzechRepublic()) {
			$form->addText('zip', 'form_label_zip')->setRequired()->setMaxLength(50);
		} else {
			$form->addText('city', 'form_label_city')->setRequired()->setMaxLength(100);
		}

		$form->onSuccess[] = $this->formSucceeded(...);
		$form->onError[] = $this->formError(...);
		$form->onValidate[] = $this->formValidate(...);

		$this->attachAntispamTo($form);

		return $form;
	}

	public function formValidate(UI\Form $form): void
	{
		if (
			! $this->variant->product->disableConfigurator
			&& (! isset($form->getHttpData()['selectedOptions']) || $form->getHttpData()['selectedOptions'] === '')) {
			$form->addError('form_error_bad_selected_options_of_configuration');
		}
	}
	public function formError(UI\Form $form): void
	{
		if (isset($form->getHttpData()['selectedOptions'])) {
			$this->selectedOptions = $form->getHttpData()['selectedOptions'];
		}
		if (isset($form->getHttpData()['price'])) {
			$this->price = (int) $form->getHttpData()['price'];
		}
		if ($this->presenter->isAjax()) {
			$this->redrawControl();
		}
	}

	public function formSucceeded(UI\Form $form, ArrayHash $values): void
	{
		try {
			$allValues = $form->getHttpData();

			/** @var State $state */
			$state = $this->stateRepository->getByIdChecked($values->state);
			$contactLocalization = $this->contactLocalizationRepository->getByIdChecked($values->contact);

			$configurationOrder = $this->configurationOrderModel->createEntity(
				CreateConfigurationOrderDto::fromArray(array_merge($allValues, (array)$values,[
					'state' => $state,
					'mutation' => $this->mutation,
					'configurationType' => $this->configurationType,
					'contact' => $contactLocalization->contact,
					'user' => $this->user,
				])),
			);

			$configurationOrderItem = $this->configurationOrderItemModel->createEntity(
				CreateConfigurationOrderItemDto::fromArray(array_merge($allValues, (array)$values,[
					'configurationOrder' => $configurationOrder,
					'variant' => $this->variant,
					'productInstanceLocalization' => $this->productInstanceLocalization,
				])),
			);

			$this->configurationOrderRepository->flush();
			$this->configurationOrderItemRepository->flush();

			$this->flashMessage('form_configurator_order_ok', 'ok');

			$form['name']->setValue('');
			$form['email']->setValue('');
			$form['phone']->setValue('');
			$form['message']->setValue('');

			if (isset($form['zip'])) {
				$form['zip']->setValue('');
			}
			if (isset($form['city'])) {
				$form['city']->setValue('');
			}

			if (isset($form['count'])) {
				$form['count']->setValue('');
			}

			$this->selectedOptions = $allValues['selectedOptions'];
			$this->price = (int) $allValues['price'];

			$this->gtm->pushEvent((new GtmProductViewEvent($this->gtm))
				->setup(
					mutation: $this->mutation,
					variant: $configurationOrderItem->variant,
					quantity: $configurationOrderItem->count,
					price: $this->price,
					state: $configurationOrder->state,
					forcedName: 'item_requested',
				)
			);

			//hubspot
			$productLocalization = $this->variant->product->getLocalization($this->mutation);
			if($productLocalization === null) {
				Debugger::log('Missing product localization for mutation ' . $this->mutation->langCode, 'error');
				$this->flashMessage('form_configurator_order_fail', 'error');
			}else{
				$values->pageLink = $this->linkFactory->linkTranslateToNette($productLocalization, ['v' => $this->variant->id]);
				$values->page = $productLocalization->getNameTitle();
				$this->hubspotFormService->submitForm(HubspotFormService::FORM_TYPE_INQUIRY, $values, $values->pageLink, $values->page, $state, $contactLocalization);
			}

		} catch (\Throwable $e) {
			$this->flashMessage('form_configurator_order_fail', 'error');
		}


		if ($this->presenter->isAjax()) {
			$this->presenter->redrawControl('gtmEvents');
			$this->redrawControl();
		} else {
			if ($this->productInstanceLocalization === null) {
				$this->presenter->redirect('this');
			} else {
				$this->presenter->redirect('this', ['productInstance' => $this->productInstanceLocalization->id]);
			}
		}
	}

	public function render(): void
	{
		try {
			$this->template->setTranslator($this->translator);
			$this->template->selectedOptions = $this->selectedOptions;
			$this->template->price = $this->price;
			$this->template->render(__DIR__ . '/configurationOrderForm.latte');
		} catch (\Throwable $e) {
			/** @noinspection PhpUnhandledExceptionInspection */
			$this->handleRenderError500($e);
		}

	}

	protected function createComponentMessageForForm(): MessageForForm
	{
		return $this->messageForFormFactory->create($this->translator);
	}

	public function handleLoadRegions(int $stateId): void
	{
		$this->setState($this->stateRepository->getByIdChecked($stateId));
		if ($this->presenter->isAjax()) {
			$this->redrawControl('regions');
			$this->redrawControl('zip');
		}
	}

	private function getStates(): ICollection
	{
		if (!isset($this->states)) {
			$this->states = $this->stateRepository->findByIds(array_keys($this->getStateIdToContactIds()));
		}
		return $this->states;
	}

	private function getContactLocalizations(): ICollection
	{
		if ($this->contactLocalizations === null) {
			if (isset($this->getStateIdToContactIds()[$this->selectedState->id])) {
				$this->contactLocalizations = $this->contactLocalizationRepository->findByIds($this->getStateIdToContactIds()[$this->selectedState->id]);
			} else {
				$this->contactLocalizations = new EmptyCollection();
			}
		}
		return $this->contactLocalizations;

	}

	private function setState(State $newState): void
	{
		$this->contactLocalizations = null;
		$this->selectedState = $newState;
	}


}
