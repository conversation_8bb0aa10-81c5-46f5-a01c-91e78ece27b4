includes:
	- environment.dev.neon


parameters:
	stageName: local


	admin:
		allowedIpRanges:
			- "**********/16" # default docker bridge range
			- "***********/16" # default orbstack range

	config:
		domainUrl: https://ton.superkoders.test/

		elastica:
			config:
				host: ton_es
				port: 9200

		mutations:
			cs:
				domain: ton.superkoders.test
			en:
				domain: ton.superkoders.test

	migrations:
		withDummyData: false

	database:
		host: ton_db
		database: ton
		user: ton
		password: ton

	redis:
		host: ton_redis

http:
	proxy:
		- *********/8

mail:
	host: ton_mailcatcher
	port: 1025
