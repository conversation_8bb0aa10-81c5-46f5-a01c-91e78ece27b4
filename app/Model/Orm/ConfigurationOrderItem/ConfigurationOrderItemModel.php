<?php declare(strict_types=1);

namespace App\Model\Orm\ConfigurationOrderItem;

use Nextras\Orm\Model\Model;

readonly class ConfigurationOrderItemModel
{
	public function __construct(
		private ConfigurationOrderItemRepository $repository,
	)
	{
	}

	public function create(CreateConfigurationOrderItemDto $dto): ConfigurationOrderItem
	{
		$configurationOrderItem = new ConfigurationOrderItem();
		$this->repository->attach($configurationOrderItem);

		$configurationOrderItem->configurationOrder = $dto->configurationOrder;
		$configurationOrderItem->productInstance =  ($dto->productInstanceLocalization !== null)
			? $dto->productInstanceLocalization->getParent()
			: null;
		$configurationOrderItem->count = $dto->count;
		$configurationOrderItem->selectedOptions = $dto->selectedOptions;
		$configurationOrderItem->variant = $dto->variant;

		return $configurationOrderItem;
	}
}
