<?php declare(strict_types=1);

namespace App\Model\Orm\ConfigurationOrderItem;

use App\Model\Orm\ConfigurationOrder\ConfigurationOrder;
use App\Model\Orm\ProductVariant\ProductVariant;
use App\PostType\ProductInstance\Model\Orm\ProductInstance;
use Nextras\Orm\Entity\Entity;

/**
 * @property int $id {primary}
 * @property string $selectedOptions
 * @property int|null $count {default null}
 *
 * RELATIONS
 * @property ConfigurationOrder $configurationOrder {m:1 ConfigurationOrder::$items}
 * @property ProductVariant $variant {m:1 ProductVariant, oneSided=true}
 * @property ProductInstance|null $productInstance {m:1 ProductInstance, oneSided=true}
 *
 * VIRTUAL
 */
class ConfigurationOrderItem extends Entity
{

}
