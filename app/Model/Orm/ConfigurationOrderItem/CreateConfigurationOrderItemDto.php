<?php

namespace App\Model\Orm\ConfigurationOrderItem;

use App\Model\Orm\ConfigurationOrder\ConfigurationOrder;
use App\Model\Orm\ProductVariant\ProductVariant;
use App\PostType\ProductInstance\Model\Orm\ProductInstanceLocalization;

final readonly class CreateConfigurationOrderItemDto
{
	public function __construct(
		public ConfigurationOrder $configurationOrder,
		public ProductVariant $variant,
		public string $selectedOptions,
		public ?int $count = null,
		public ?ProductInstanceLocalization $productInstanceLocalization = null,
	)
	{
	}

	/**
	 * @param array<string, mixed> $data
	 * @return self
	 */
	public static function fromArray(array $data): self
	{
		return new self(
			configurationOrder: $data['configurationOrder'],
			variant: $data['variant'],
			selectedOptions: $data['selectedOptions'],
			count: $data['count'] ?? null,
			productInstanceLocalization: $data['productInstanceLocalization'] ?? null,
		);
	}
}
