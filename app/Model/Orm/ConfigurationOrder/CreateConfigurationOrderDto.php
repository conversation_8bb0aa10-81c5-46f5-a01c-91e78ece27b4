<?php declare(strict_types=1);

namespace App\Model\Orm\ConfigurationOrder;

use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\ProductVariant\ProductVariant;
use App\Model\Orm\State\State;
use App\Model\Orm\User\User;
use App\PostType\Contact\Model\Orm\Contact;
use App\PostType\ProductInstance\Model\Orm\ProductInstanceLocalization;


final readonly class CreateConfigurationOrderDto
{
    public function __construct(
        public Mutation $mutation,
        public ConfigurationType $configurationType,
        public State $state,
        public Contact $contact,
        public string $name,
        public string $email,
        public string $phone,
        public string $text,
        public ?string $zip = '',
        public ?string $city = '',
        public ?User $user = null
    ) {}

	/**
	 * @param array<string, mixed> $data
	 * @return self
	 */
    public static function fromArray(array $data): self
    {
        return new self(
            mutation: $data['mutation'],
            configurationType: $data['configurationType'],
            state: $data['state'],
            contact: $data['contact'],
            name: $data['name'],
            email: $data['email'],
            phone: $data['phone'],
            text: $data['text'],
            zip: $data['zip'] ?? '',
            city: $data['city'] ?? '',
            user: $data['user'] ?? null
        );
    }
}
