<?php declare(strict_types=1);

namespace App\Model\Orm\ConfigurationOrder;

use App\Model\Configurator\ConfigurationOrderRequester;
use App\Model\Email\ConfigurationSender;
use App\Model\Orm\EmailTemplate\EmailTemplate;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Mutation\MutationRepository;
use App\Model\Orm\Orm;
use App\Model\Orm\State\State;
use App\Model\Orm\State\StateRepository;
use App\Model\TranslatorDB;
use App\Utils\DateTime;
use Nette\Utils\Random;
use Nextras\Dbal\Utils\DateTimeImmutable;
use Tracy\Debugger;

readonly class ConfigurationOrderModel
{

	public function __construct(
		private ConfigurationOrderRepository $configurationOrderRepository,
		private MutationRepository $mutationRepository,
		private TranslatorDB $translatorDB,
		private Orm $orm,
		private ConfigurationOrderRequester $configurationOrderRequester,
		private StateRepository $stateRepository,
		private ConfigurationSender $configurationSender,
	)
	{}

	public function createEntity(CreateConfigurationOrderDto $dto): ConfigurationOrder
	{
		$configurationOrder = new ConfigurationOrder();
		$this->configurationOrderRepository->attach($configurationOrder);

		$configurationOrder->contact = $dto->contact;
		$configurationOrder->state = $dto->state;
		$configurationOrder->name = $dto->name;
		$configurationOrder->email = $dto->email;
		$configurationOrder->configurationType = $dto->configurationType;
		$configurationOrder->phone = $dto->phone;
		$configurationOrder->mutation = $dto->mutation;
		$configurationOrder->user = $dto->user;
		$configurationOrder->text = $dto->text;
		$configurationOrder->zip = $dto->zip;
		$configurationOrder->city = $dto->city;
		$configurationOrder->hash = Random::generate(100);

		$this->configurationOrderRepository->persist($configurationOrder);

		return $configurationOrder;
	}


	public function sendNotice(ConfigurationOrder $configurationOrder): void
	{
		if ($configurationOrder->noticeSentAt === null) {
			$this->configurationSender->sendToCompany(
				$configurationOrder,
				EmailTemplate::KEY_ORDER_NOTIFY
			);

			$configurationOrder->noticeSentAt = 'now';
			$this->configurationOrderRepository->persistAndFlush($configurationOrder);
		}
	}

	public function sendErrorAlert(ConfigurationOrder $configurationOrder, string $errorMessage): void
	{
		if ($configurationOrder->errorSentAt === null) {
			if($this->configurationSender->sendErrorAllert($configurationOrder, $errorMessage)) {
				$configurationOrder->errorSentAt = 'now';
				$this->configurationOrderRepository->persistAndFlush($configurationOrder);
			}
		}
	}

	public function sendInquiry(ConfigurationOrder $configurationOrder): void
	{
		if ($configurationOrder->inquirySentAt === null) {

			$this->configurationSender->sendToCompany(
				$configurationOrder,
				EmailTemplate::KEY_ORDER_INQUIRY,
			);

			$configurationOrder->inquirySentAt = 'now';
			$this->configurationOrderRepository->persistAndFlush($configurationOrder);
		}
	}

	public function setDealerAnsweredByHash(string $hash): bool
	{
		$configurationOrder = $this->configurationOrderRepository->getBy([
			'hash' => $hash
		]);

		if ($configurationOrder !== null) {
			$this->setDealerAnswered($configurationOrder);
			return true;
		} else {
			return false;
		}
	}


	public function setDealerAnswered(ConfigurationOrder $configurationOrder): void
	{
		$configurationOrder->offerSentAt = 'now';
		$this->configurationOrderRepository->persistAndFlush($configurationOrder);
	}



	public function checkOrders(): void
	{
		$mutation = $this->mutationRepository->getBy(['langCode' => Mutation::CODE_EN]);

		if($mutation === null) {
			return;
		}

		$this->orm->setMutation($mutation);
		$this->translatorDB->reInit($mutation);

		/**
		 * @var State $czechia
		 */
		$czechia = $this->stateRepository->getByChecked([
			'code' => State::DEFAULT_CODE
		]);

		$today = new DateTime(state: $czechia);
		$checkDate = (new DateTimeImmutable())->modify('- 1 day');
		$configurationOrders = $this->configurationOrderRepository->findBy([
			'offerSentAt' => null,
			'inquirySentAt' => null,
			'createdAt<=' => $checkDate,
		]);
		if ($today->isWorkingDay()) {
			foreach ($configurationOrders as $configurationOrder) {
				$this->sendInquiry($configurationOrder);
			}
		}
	}

    public function createOrders(): int
    {
        $ordersCount = 0;
        $newConfigurationOrders = $this->configurationOrderRepository->findBy([
            'selectedOptions!=' => '',
            'code' => '',
        ]);

        foreach ($newConfigurationOrders as $newConfigurationOrder) {

						try {
							if ($this->configurationOrderRequester->makeRequest($newConfigurationOrder)) {
								$this->sendNotice($newConfigurationOrder);
								$this->sendInfoToCustomer($newConfigurationOrder);
							}
						} catch (\Throwable $e) {
							$this->sendErrorAlert($newConfigurationOrder, $e->getMessage());
							Debugger::log($e, Debugger::EXCEPTION);

						}

        }
        return $ordersCount;
    }

	public function createOrdersWithoutConfiguration(): int
	{
		$ordersCount = 0;
		$newConfigurationOrders = $this->configurationOrderRepository->findBy([
			'selectedOptions' => '',
			'noticeSentAt' => null,
		]);

		foreach ($newConfigurationOrders as $newConfigurationOrder) {
			$this->sendNotice($newConfigurationOrder);
			$this->sendInfoToCustomer($newConfigurationOrder);
		}
		return $ordersCount;
	}

	private function sendInfoToCustomer(ConfigurationOrder $configurationOrder): void
	{
		if ($configurationOrder->noticeToCustomerSentAt === null) {
			$this->configurationSender->sendToCustomer(
				$configurationOrder
			);

			$configurationOrder->noticeToCustomerSentAt = 'now';
			$this->configurationOrderRepository->persistAndFlush($configurationOrder);
		}
	}


}
