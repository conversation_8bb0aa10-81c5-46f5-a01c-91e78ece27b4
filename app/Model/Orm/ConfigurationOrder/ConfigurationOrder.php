<?php declare(strict_types = 1);

namespace App\Model\Orm\ConfigurationOrder;

use App\Model\Orm\ConfigurationOrderItem\ConfigurationOrderItem;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\State\State;
use App\Model\Orm\User\User;
use App\PostType\Contact\Model\Orm\Contact;
use Nextras\Dbal\Utils\DateTimeImmutable;
use Nextras\Orm\Entity\Entity;
use Nextras\Orm\Relationships\HasMany;

/**
 * @property int $id {primary}
 * @property string $name
 * @property string $email
 * @property string $code {default  ''}
 * @property ConfigurationType $configurationType {wrapper BackedEnumWrapper}
 * @property string $phone
 * @property string $text
 * @property string $zip {default ''}
 * @property string $city {default ''}
 * @property string $hash
 * @property DateTimeImmutable $createdAt {default 'now'}
 * @property DateTimeImmutable|null $noticeToCustomerSentAt
 * @property DateTimeImmutable|null $noticeSentAt
 * @property DateTimeImmutable|null $inquirySentAt
 * @property DateTimeImmutable|null $offerSentAt
 * @property DateTimeImmutable|null $errorSentAt
 *
 * RELATIONS
 * @property Mutation|null $mutation {m:1 Mutation, oneSided=true}
 * @property State $state {m:1 State, oneSided=true}
 * @property Contact|null $contact {m:1 Contact, oneSided=true}
 * @property User|null $user {m:1 User::$configurationOrders}
 * @property HasMany|ConfigurationOrderItem[] $items {1:m ConfigurationOrderItem::$configurationOrder}
 *
 * VIRTUAL
 */
class ConfigurationOrder extends Entity
{
}
