<?php declare(strict_types = 1);

namespace App\Model\Orm;

use App\Model\Orm\Admin\AdminRepository;
use App\Model\Orm\Alias\AliasRepository;
use App\Model\Orm\AliasHistory\AliasHistoryRepository;
use App\Model\Orm\ApiToken\ApiTokenRepository;
use App\Model\Orm\ApiUser\ApiUserRepository;
use App\Model\Orm\AttachedFile\AttachedFileRepository;
use App\Model\Orm\ConfigurationOrder\ConfigurationOrderRepository;
use App\Model\Orm\ConfigurationOrderItem\ConfigurationOrderItemRepository;
use App\Model\Orm\ConfiguratorTranslation\ConfiguratorTranslationRepository;
use App\Model\Orm\ConfiguratorTranslationExtender\ConfiguratorTranslationExtenderRepository;
use App\Model\Orm\Document\DocumentRepository;
use App\Model\Orm\DocumentGroup\DocumentGroupRepository;
use App\Model\Orm\DocumentGroupProduct\DocumentGroupProductRepository;
use App\Model\Orm\EmailTemplate\EmailTemplateRepository;
use App\Model\Orm\EmailTemplateFile\EmailTemplateFileRepository;
use App\Model\Orm\EsIndex\EsIndexRepository;
use App\Model\Orm\File\FileRepository;
use App\Model\Orm\ImportCache\ImportCacheRepository;
use App\Model\Orm\Inquiry\InquiryRepository;
use App\Model\Orm\IpGeolocation\IpGeolocationRepository;
use App\Model\Orm\LibraryImage\LibraryImageRepository;
use App\Model\Orm\LibraryTree\LibraryTreeRepository;
use App\Model\Orm\Log\LogRepository;
use App\Model\Orm\Mutation\Mutation;
use App\Model\Orm\Mutation\MutationRepository;
use App\Model\Orm\MutationSetting\MutationSettingRepository;
use App\Model\Orm\NewsletterEmail\NewsletterEmailRepository;
use App\Model\Orm\Parameter\ParameterRepository;
use App\Model\Orm\ParameterValue\ParameterValueRepository;
use App\Model\Orm\PriceLevel\PriceLevelRepository;
use App\Model\Orm\Product\ProductRepository;
use App\Model\Orm\ProductFile\ProductFileRepository;
use App\Model\Orm\ProductGroup\ProductGroupRepository;
use App\Model\Orm\ProductImage\ProductImageRepository;
use App\Model\Orm\ProductLocalization\ProductLocalizationRepository;
use App\Model\Orm\ProductProduct\ProductProductRepository;
use App\Model\Orm\ProductReview\ProductReviewRepository;
use App\Model\Orm\ProductTree\ProductTreeRepository;
use App\Model\Orm\ProductVariant\ProductVariantRepository;
use App\Model\Orm\ProductVariantLocalization\ProductVariantLocalizationRepository;
use App\Model\Orm\ProductVariantPrice\ProductVariantPriceRepository;
use App\Model\Orm\Redirect\RedirectRepository;
use App\Model\Orm\State\StateRepository;
use App\Model\Orm\Stock\StockRepository;
use App\Model\Orm\String\StringRepository;
use App\Model\Orm\Supply\SupplyRepository;
use App\Model\Orm\TreeProduct\TreeProductRepository;
use App\Model\Orm\Watchdog\WatchdogRepository;
use App\PostType\Career\Model\Orm\CareerLocalizationRepository;
use App\PostType\Career\Model\Orm\CareerRepository;
use App\PostType\Collection\Model\Orm\CollectionLocalizationRepository;
use App\PostType\Collection\Model\Orm\CollectionRepository;
use App\PostType\Contact\Model\Orm\ContactLocalizationRepository;
use App\PostType\Contact\Model\Orm\ContactRepository;
use App\PostType\Page\Model\Orm\TreeParentRepository;
use App\PostType\Page\Model\Orm\TreeRepository;
use App\Model\Orm\TreeTree\TreeTreeRepository;
use App\Model\Orm\User\UserRepository;
use App\Model\Orm\UserHash\UserHashRepository;
use App\Model\Orm\UserMutation\UserMutationRepository;
use App\PostType\Author\Model\Orm\AuthorLocalizationRepository;
use App\PostType\Author\Model\Orm\AuthorRepository;
use App\PostType\Blog\Model\Orm\BlogLocalizationRepository;
use App\PostType\Blog\Model\Orm\BlogLocalizationTreeRepository;
use App\PostType\Blog\Model\Orm\BlogRepository;
use App\PostType\BlogTag\Model\Orm\BlogTag\BlogTagLocalizationRepository;
use App\PostType\BlogTag\Model\Orm\BlogTag\BlogTagRepository;
use App\PostType\Press\Model\Orm\PressLocalizationRepository;
use App\PostType\Press\Model\Orm\PressRepository;
use App\PostType\ProductInstance\Model\Orm\ProductInstanceLocalizationRepository;
use App\PostType\ProductInstance\Model\Orm\ProductInstanceRepository;
use App\PostType\Reference\Model\Orm\ReferenceLocalizationRepository;
use App\PostType\Reference\Model\Orm\ReferenceRepository;
use App\PostType\SeoLink\Model\Orm\SeoLinkLocalizationRepository;
use App\PostType\SeoLink\Model\Orm\SeoLinkRepository;
use App\PostType\Service\Model\Orm\ServiceLocalizationRepository;
use App\PostType\Service\Model\Orm\ServiceRepository;
use App\PostType\Showroom\Model\Orm\ShowroomLocalizationRepository;
use App\PostType\Showroom\Model\Orm\ShowroomRepository;
use LogicException;
use Nextras\Orm\Model\Model;

/**
 * @property-read TreeRepository $tree
 * @property-read TreeParentRepository $treeParent
 * @property-read AliasRepository $alias
 * @property-read AttachedFileRepository $attachedFile
 * @property-read LibraryTreeRepository $libraryTree
 * @property-read LibraryImageRepository $libraryImage
 * @property-read UserRepository $user
 * @property-read UserHashRepository $userHash
 * @property-read AliasHistoryRepository $aliasHistory
 * @property-read RedirectRepository $redirect
 * @property-read FileRepository $file
 * @property-read MutationRepository $mutation
 * @property-read EmailTemplateRepository $emailTemplate
 * @property-read EmailTemplateFileRepository $emailTemplateFile
 * @property-read EsIndexRepository $esIndex
 * @property-read TreeTreeRepository $treeTree
 * @property-read StateRepository $state
 * @property-read BlogRepository $blog
 * @property-read BlogLocalizationRepository $blogLocalization
 * @property-read BlogLocalizationTreeRepository $blogLocalizationTree
 * @property-read BlogTagRepository $blogTag
 * @property-read BlogTagLocalizationRepository $blogTagLocalization
 * @property-read AuthorRepository $author
 * @property-read AuthorLocalizationRepository $authorLocalization
 * @property-read UserMutationRepository $userMutation
 * @property-read NewsletterEmailRepository $newsletterEmail
 * @property-read ApiUserRepository $apiUser
 * @property-read StringRepository $string
 * @property-read ParameterRepository $parameter
 * @property-read ParameterValueRepository $parameterValue
 * @property-read PriceLevelRepository $priceLevel
 * @property-read ProductRepository $product
 * @property-read ProductFileRepository $productFile
 * @property-read ProductImageRepository $productImage
 * @property-read ProductLocalizationRepository $productLocalization
 * @property-read ProductProductRepository $productProduct
 * @property-read ProductReviewRepository $productReview
 * @property-read ProductTreeRepository $productTree
 * @property-read ProductVariantRepository $productVariant
 * @property-read ProductVariantLocalizationRepository $productVariantLocalization
 * @property-read ProductVariantPriceRepository $productVariantPrice
 * @property-read StockRepository $stock
 * @property-read SupplyRepository $supply
 * @property-read TreeProductRepository $treeProduct
 * @property-read SeoLinkRepository $seoLink
 * @property-read SeoLinkLocalizationRepository $seoLinkLocalization
 * @property-read ApiTokenRepository $apiToken
 * @property-read ImportCacheRepository $importCache
 * @property-read MutationSettingRepository $mutationSetting
 * @property-read PressRepository $press
 * @property-read PressLocalizationRepository $pressLocalization
 * @property-read ShowroomRepository $showroom
 * @property-read ShowroomLocalizationRepository $showroomLocalization
 * @property-read ContactRepository $contact
 * @property-read ContactLocalizationRepository $contactLocalization
 * @property-read CareerRepository $career
 * @property-read CareerLocalizationRepository $careerLocalization
 * @property-read ReferenceRepository $reference
 * @property-read ReferenceLocalizationRepository $referenceLocalization
 * @property-read ServiceRepository $service
 * @property-read ServiceLocalizationRepository $serviceLocalization
 * @property-read LogRepository $log
 * @property-read IpGeolocationRepository $ipGeolocation
 * @property-read DocumentGroupRepository $documentGroup
 * @property-read DocumentGroupProductRepository $documentGroupProduct
 * @property-read DocumentRepository $document
 * @property-read ProductGroupRepository $productGroup
 * @property-read AdminRepository $admin
 * @property-read InquiryRepository $inquiry
 * @property-read ProductInstanceLocalizationRepository $productInstanceLocalization
 * @property-read ProductInstanceRepository $productInstance
 * @property-read ConfiguratorTranslationRepository $configuratorParameter
 * @property-read ConfigurationOrderRepository $configurationOrderRepository
 * @property-read CollectionRepository $collection
 * @property-read CollectionLocalizationRepository $collectionLocalization
 * @property-read ConfiguratorTranslationExtenderRepository $configuratorTranslationExtender
 * @property-read WatchdogRepository $watchdog
 * @property-read ConfigurationOrderItemRepository $configurationOrderItemRepository
 */
final class Orm extends Model
{

	private ?Mutation $selectedMutation = null;

	private bool $globalPublicOnly = true;

	public function setMutation(Mutation $mutation): void
	{
		$this->selectedMutation = $mutation;
	}


	public function getMutation(): Mutation
	{
		if ($this->selectedMutation === null) {
			throw new LogicException('ORM mutation setup missing.');
		}

		return $this->selectedMutation;
	}


	public function hasMutation(): bool
	{
		return $this->selectedMutation !== null;
	}


	public function setPublicOnly(bool $globalPublicOnly = true): void
	{
		$this->globalPublicOnly = $globalPublicOnly;
	}


	public function getPublicOnly(): bool
	{
		return $this->globalPublicOnly;
	}

}
